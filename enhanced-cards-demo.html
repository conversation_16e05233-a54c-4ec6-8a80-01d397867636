<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Enhanced Skill Cards Demo</title>
    <style>
      :root {
        --color-primary: #5462ff;
      }

      body {
        margin: 0;
        padding: 2rem;
        background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
        color: white;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .demo-container {
        position: relative;
        width: 100%;
        max-width: 800px;
        height: 400px;
      }

      /* Include the enhanced card styles */
      .stack__card {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 12;
        width: 100%;
        display: flex;
        justify-content: space-around;
        gap: 2rem;
        flex-wrap: wrap;
      }

      .card__container {
        padding: 1.5rem 1rem;
        width: 10rem;
        height: 10rem;
        background: rgba(15, 23, 42, 0.85);
        backdrop-filter: blur(20px);
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(0, 0, 0, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);
        text-align: center;
        color: #fff;
        border: 1px solid rgba(255, 255, 255, 0.08);
        position: relative;
        overflow: hidden;
        transform: translateY(0) scale(1);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .card__container::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(
          90deg,
          transparent 0%,
          var(--color-primary) 50%,
          transparent 100%
        );
        transform: scaleX(0);
        transition: transform 0.4s ease;
      }

      .card__container:hover {
        transform: translateY(-12px) scale(1.05);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5),
          0 8px 16px rgba(84, 98, 255, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.15);
        border-color: rgba(84, 98, 255, 0.3);
        background: rgba(15, 23, 42, 0.95);
      }

      .card__container:hover::before {
        transform: scaleX(1);
      }

      .icon__container {
        position: relative;
        margin-bottom: 1rem;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 80px;
      }

      .icon__container .card__icon {
        width: 70px;
        height: 70px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.3));
      }

      .card__container:hover .card__icon {
        transform: scale(1.1) rotate(3deg);
        filter: drop-shadow(0 8px 20px rgba(84, 98, 255, 0.4));
      }

      .card__title {
        font-size: 1rem;
        font-weight: 600;
        margin: 0.5rem 0 1rem 0;
        color: #ffffff;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
      }

      .card__container:hover .card__title {
        color: #ffffff;
        transform: translateY(-2px);
      }

      .card__progress-container {
        width: 100%;
        height: 8px;
        border-radius: 12px;
        margin-top: auto;
        overflow: hidden;
        background: rgba(255, 255, 255, 0.08);
        transform: translateY(15px);
        opacity: 0;
        border: none;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
      }

      .card__progress-container::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent 0%,
          rgba(255, 255, 255, 0.1) 50%,
          transparent 100%
        );
        animation: shimmer 2s infinite;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .card__container:hover .card__progress-container {
        transform: translateY(0px);
        opacity: 1;
      }

      .card__container:hover .card__progress-container::before {
        opacity: 1;
      }

      .card__progressbar {
        height: 100%;
        background: linear-gradient(
          90deg,
          var(--color-primary) 0%,
          #6366f1 100%
        );
        font-size: 0.7rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        border-radius: 12px;
        position: relative;
        overflow: hidden;
        transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 8px rgba(84, 98, 255, 0.3);
      }

      .card__progressbar::after {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.3),
          transparent
        );
        animation: progressShine 2s infinite;
      }

      @keyframes shimmer {
        0% {
          transform: translateX(-100%);
        }
        100% {
          transform: translateX(100%);
        }
      }

      @keyframes progressShine {
        0% {
          left: -100%;
        }
        100% {
          left: 100%;
        }
      }

      .demo-title {
        position: absolute;
        top: -60px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 1.5rem;
        font-weight: 600;
        color: #ffffff;
        text-align: center;
      }
    </style>
  </head>
  <body>
    <div class="demo-container">
      <h2 class="demo-title">Enhanced Skill Cards</h2>
      <div class="stack__card">
        <!-- Vue.js Card -->
        <div class="card__container">
          <div class="icon__container">
            <img
              src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/vuejs/vuejs-original.svg"
              alt="Vue.js"
              class="card__icon"
            />
          </div>
          <h3 class="card__title">Vuejs</h3>
          <div class="card__progress-container">
            <div class="card__progressbar" style="width: 60%">60%</div>
          </div>
        </div>

        <!-- Git Card -->
        <div class="card__container">
          <div class="icon__container">
            <img
              src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/git/git-original.svg"
              alt="Git"
              class="card__icon"
            />
          </div>
          <h3 class="card__title">Git</h3>
          <div class="card__progress-container">
            <div class="card__progressbar" style="width: 60%">60%</div>
          </div>
        </div>

        <!-- HTML Card -->
        <div class="card__container">
          <div class="icon__container">
            <img
              src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/html5/html5-original.svg"
              alt="HTML5"
              class="card__icon"
            />
          </div>
          <h3 class="card__title">HTML</h3>
          <div class="card__progress-container">
            <div class="card__progressbar" style="width: 80%">80%</div>
          </div>
        </div>

        <!-- CSS Card -->
        <div class="card__container">
          <div class="icon__container">
            <img
              src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/css3/css3-original.svg"
              alt="CSS3"
              class="card__icon"
            />
          </div>
          <h3 class="card__title">CSS</h3>
          <div class="card__progress-container">
            <div class="card__progressbar" style="width: 80%">80%</div>
          </div>
        </div>

        <!-- JavaScript Card -->
        <div class="card__container">
          <div class="icon__container">
            <img
              src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/javascript/javascript-original.svg"
              alt="JavaScript"
              class="card__icon"
            />
          </div>
          <h3 class="card__title">JavaScript</h3>
          <div class="card__progress-container">
            <div class="card__progressbar" style="width: 70%">70%</div>
          </div>
        </div>

        <!-- React Card -->
        <div class="card__container">
          <div class="icon__container">
            <img
              src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg"
              alt="React"
              class="card__icon"
            />
          </div>
          <h3 class="card__title">React</h3>
          <div class="card__progress-container">
            <div class="card__progressbar" style="width: 70%">70%</div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
