/* Enhanced Project Cards Styling */
.project__section .container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  width: 100%;
  margin-top: 3rem;
  padding: 0 1rem;
}

.project__section h2 {
  font-size: clamp(2rem, 4vw, 2.8rem);
  font-weight: 400;
  margin: 0.5rem;
  color: var(--color-secondary-dark);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.02em;
}

.project__section h2 span {
  font-size: clamp(1.5rem, 3vw, 2rem);
  color: var(--color-primary);
  font-weight: 300;
  opacity: 0.8;
}

.project__section p {
  font-size: clamp(0.9rem, 1.5vw, 1rem);
  color: var(--color-secondary-text);
  margin-top: 0.5rem;
  font-weight: 500;
}

.project__section .container > div {
  position: relative;
  cursor: pointer;
  height: 220px;
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.project__section .container > div:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.85) 0%,
    rgba(84, 98, 255, 0.9) 100%
  );
  color: var(--overlay-text);
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border-radius: 12px;
}

span.overlay-stack {
  margin: 0.25rem;
  font-size: 0.75rem;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-weight: 500;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease forwards;
  animation-delay: calc(var(--i) * 0.1s);
  opacity: 0;
  transform: translateY(20px);
}

.overlay-description {
  font-size: 0.9rem;
  margin: 0 0 1rem 0;
  text-align: center;
  line-height: 1.5;
  font-weight: 400;
  opacity: 0.95;
}

.project__section .container > div:hover .overlay {
  opacity: 1;
  visibility: visible;
}

.project__section .container > div:hover span.overlay-stack {
  opacity: 1;
  transform: translateY(0);
}

.project__section .container > div:nth-of-type(1) {
  grid-column: 1/3;
}

.project__section .container > div:nth-child(6) {
  grid-column: 3/5;
}

.project__section .container > div:nth-child(9) {
  grid-column: 3/5;
}

.project__section .container > div:nth-child(10) {
  grid-column: 1/3;
}

.bg1 {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
  color: var(--text-color);
  position: relative;
}

.bg1::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    transparent 0%,
    rgba(84, 98, 255, 0.05) 100%
  );
  border-radius: 12px;
  transition: opacity 0.3s ease;
}

.bg1:hover::before {
  opacity: 0.7;
}

.bg2 {
  background: linear-gradient(135deg, #f0f2f5 0%, #dde1e7 100%);
  color: var(--text-color);
  font-weight: 400;
  position: relative;
}

.bg2::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    transparent 0%,
    rgba(84, 98, 255, 0.08) 100%
  );
  border-radius: 12px;
  transition: opacity 0.3s ease;
}

.bg2:hover::before {
  opacity: 0.8;
}

.project-overlay {
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.5);
  padding: 10px;
  border-radius: 5px;
}

.project-overlay h3 {
  margin: 0;
  font-size: 20px;
}

.project-overlay p {
  margin: 5px 0 0;
  font-size: 14px;
}

/* Modal Styles */
.modal-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  z-index: 1000;
  animation: fadeIn 0.3s;
}

.modal-content {
  background: #fff;
  padding: 20px;
  border-radius: 10px;
  max-width: 80%;
  max-height: 80%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.modal__container-image {
  width: 100%;
  height: auto;
  margin-bottom: 20px;
}

.modal__image {
  width: 400px;
  height: auto;
}

.modal-info {
  text-align: center;
}

.modal-info h2 {
  margin: 0 10px;
  font-size: 18px;
  color: #333;
}

.modal-info p {
  font-size: 16px;
  color: #666;
  width: 60%;
  margin: auto;
  text-align: justify;
  word-spacing: 1px;
}

.modal__stacks .stack {
  font-size: 12px;
  color: var(--color-light);
  padding: 7px;
  border-radius: 5px;
  margin: 20px 5px;
  display: inline-block;
  background: var(--color-primary);
}

.modal__git-link {
  padding: 0.5rem 1.5rem;
  color: #fff;
  text-decoration: none;
  border-radius: 0.5rem;

  background-color: var(--color-secondary-dark);
  display: inline-flex;
  align-items: center;
  transition: var(--transition);
}

.modal__git-link:hover {
  background-color: var(--color-primary); /* Change to your primary color */
}

.modal__git-link i {
  margin-right: 5px; /* Space between icon and text */
}

/* Enhanced Project Card Interactions */
.project__section .container .project-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.project__section .container .project-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 40px rgba(84, 98, 255, 0.15);
}

.project__section .container .project-card:focus {
  outline: 3px solid var(--color-primary);
  outline-offset: 4px;
  transform: translateY(-4px);
}

.project__section .container .project-card:active {
  transform: translateY(-2px) scale(1.01);
  transition: transform 0.1s ease;
}

/* Content wrapper improvements */
.content-wrapper {
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease;
}

.project__section .container > div:hover .content-wrapper {
  transform: translateY(-5px);
}

/* Loading state for project cards */
.project-card.loading {
  opacity: 0.7;
  pointer-events: none;
}

.project-card.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--color-primary);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.modal-background {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-background.open {
  opacity: 1;
}

/* close modal button */

.modal-close {
  /* Positioning */
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;

  /* Dimensions and Layout */
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;

  /* Visual Design */
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  backdrop-filter: blur(5px);
  color: var(--color-light);
  font-size: 20px;
  cursor: pointer;
  transition: var(--transition);
}

.modal-close:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.modal-close:active {
  transform: scale(0.95);
}

/* Keyframe Animations */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
  .project__section .container {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.25rem;
  }

  .project__section .container > div:nth-of-type(1) {
    grid-column: 1/3;
  }
}

@media (max-width: 992px) {
  .project__section .container {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    padding: 0 0.5rem;
  }

  .project__section .container > div {
    height: 200px;
  }

  .project__section .container > div:nth-of-type(1) {
    grid-column: 1/3;
  }
}

@media (max-width: 768px) {
  .project__section .container {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-top: 2rem;
  }

  .project__section .container > div {
    height: 180px;
  }

  .overlay {
    padding: 1rem;
  }

  .overlay-description {
    font-size: 0.8rem;
  }

  span.overlay-stack {
    font-size: 0.7rem;
    padding: 0.3rem 0.6rem;
  }
}

@media (max-width: 576px) {
  .project__section .container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .project__section .container > div {
    height: 200px;
  }

  .project__section .container > div:nth-of-type(1) {
    grid-column: 1;
  }

  .project__section h2 {
    text-align: center;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .project__section .container > div,
  .overlay,
  span.overlay-stack,
  .content-wrapper {
    transition: none;
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .project__section .container > div {
    border: 2px solid var(--color-primary);
  }

  .overlay {
    background: rgba(0, 0, 0, 0.95);
  }

  span.overlay-stack {
    border: 2px solid white;
    background: var(--color-primary);
  }
}
