/* Enhanced Project Cards Styling */
.project__section .container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  width: 100%;
  margin-top: 3rem;
  padding: 0 1rem;
}

.project__section h2 {
  font-size: clamp(2rem, 4vw, 2.8rem);
  font-weight: 400;
  margin: 0.5rem;
  color: var(--color-secondary-dark);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.02em;
}

.project__section h2 span {
  font-size: clamp(1.5rem, 3vw, 2rem);
  color: var(--color-primary);
  font-weight: 300;
  opacity: 0.8;
}

.project__section p {
  font-size: clamp(0.9rem, 1.5vw, 1rem);
  color: var(--color-secondary-text);
  margin-top: 0.5rem;
  font-weight: 500;
}

.project__section .container > div {
  position: relative;
  cursor: pointer;
  height: 220px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced styling for cards with background images */
.project__section .container > div::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.4) 100%
  );
  border-radius: 12px;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.project__section .container > div:hover::before {
  opacity: 0.7;
}

.project__section .container > div:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.85) 0%,
    rgba(84, 98, 255, 0.9) 100%
  );
  color: var(--overlay-text);
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border-radius: 12px;
}

span.overlay-stack {
  margin: 0.25rem;
  font-size: 0.75rem;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-weight: 500;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease forwards;
  animation-delay: calc(var(--i) * 0.1s);
  opacity: 0;
  transform: translateY(20px);
}

.overlay-description {
  font-size: 0.9rem;
  margin: 0 0 1rem 0;
  text-align: center;
  line-height: 1.5;
  font-weight: 400;
  opacity: 0.95;
}

.project__section .container > div:hover .overlay {
  opacity: 1;
  visibility: visible;
}

.project__section .container > div:hover span.overlay-stack {
  opacity: 1;
  transform: translateY(0);
}

.project__section .container > div:nth-of-type(1) {
  grid-column: 1/3;
}

.project__section .container > div:nth-child(6) {
  grid-column: 3/5;
}

.project__section .container > div:nth-child(9) {
  grid-column: 3/5;
}

.project__section .container > div:nth-child(10) {
  grid-column: 1/3;
}

.bg1 {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
  color: var(--text-color);
  position: relative;
}

.bg1::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    transparent 0%,
    rgba(84, 98, 255, 0.05) 100%
  );
  border-radius: 12px;
  transition: opacity 0.3s ease;
}

.bg1:hover::before {
  opacity: 0.7;
}

.bg2 {
  background: linear-gradient(135deg, #f0f2f5 0%, #dde1e7 100%);
  color: var(--text-color);
  font-weight: 400;
  position: relative;
}

.bg2::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    transparent 0%,
    rgba(84, 98, 255, 0.08) 100%
  );
  border-radius: 12px;
  transition: opacity 0.3s ease;
}

.bg2:hover::before {
  opacity: 0.8;
}

/* Enhanced styling for cards with background images */
.project-card[style*="background-image"] .content-wrapper {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.project-card[style*="background-image"] h2,
.project-card[style*="background-image"] p {
  color: white;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.7);
}

.project-card[style*="background-image"]:hover .content-wrapper {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-5px);
}

.project-overlay {
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.5);
  padding: 10px;
  border-radius: 5px;
}

.project-overlay h3 {
  margin: 0;
  font-size: 20px;
}

.project-overlay p {
  margin: 5px 0 0;
  font-size: 14px;
}

/* Enhanced Modal Styles */
.modal-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.8) 100%
  );
  backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-background.open {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  padding: 0;
  border-radius: 20px;
  max-width: min(90vw, 800px);
  max-height: 90vh;
  overflow: hidden;
  display: grid;
  grid-template-columns: 1fr 1fr;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  transform: scale(0.9) translateY(20px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.modal-background.open .modal-content {
  transform: scale(1) translateY(0);
}

.modal__image-container {
  position: relative;
  background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 20px 0 0 20px;
}

.modal__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal__image:hover {
  transform: scale(1.05);
}

.modal-info {
  padding: 2.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 1.5rem;
  background: white;
  border-radius: 0 20px 20px 0;
  position: relative;
}

.modal-info::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary) 0%, #6366f1 100%);
  border-radius: 0 20px 0 0;
}

.modal-title {
  font-size: clamp(1.5rem, 4vw, 2rem);
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.modal__description {
  font-size: 1rem;
  color: var(--color-secondary-text);
  line-height: 1.6;
  margin: 0;
  text-align: left;
}

/* Enhanced Stack Tags */
.modal__stacks {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 0;
}

.modal__stacks .stack {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--color-primary);
  padding: 0.5rem 1rem;
  border-radius: 25px;
  background: rgba(84, 98, 255, 0.1);
  border: 1px solid rgba(84, 98, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modal__stacks .stack::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transition: left 0.5s;
}

.modal__stacks .stack:hover {
  background: var(--color-primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(84, 98, 255, 0.3);
}

.modal__stacks .stack:hover::before {
  left: 100%;
}

/* Enhanced Action Buttons */
.modal__actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.modal__git-link {
  padding: 0.75rem 1.5rem;
  color: white;
  text-decoration: none;
  border-radius: 12px;
  background: linear-gradient(
    135deg,
    var(--color-secondary-dark) 0%,
    #2d3748 100%
  );
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: none;
  cursor: pointer;
}

.modal__git-link::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--color-primary) 0%, #6366f1 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal__git-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.modal__git-link:hover::before {
  opacity: 1;
}

.modal__git-link i,
.modal__git-link span {
  position: relative;
  z-index: 1;
}

.modal__demo-link {
  padding: 0.75rem 1.5rem;
  color: var(--color-primary);
  text-decoration: none;
  border-radius: 12px;
  background: transparent;
  border: 2px solid var(--color-primary);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.modal__demo-link:hover {
  background: var(--color-primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(84, 98, 255, 0.25);
}

/* Enhanced Project Card Interactions */
.project__section .container .project-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.project__section .container .project-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 40px rgba(84, 98, 255, 0.15);
}

.project__section .container .project-card:focus {
  outline: 3px solid var(--color-primary);
  outline-offset: 4px;
  transform: translateY(-4px);
}

.project__section .container .project-card:active {
  transform: translateY(-2px) scale(1.01);
  transition: transform 0.1s ease;
}

/* Content wrapper improvements */
.content-wrapper {
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.project__section .container > div:hover .content-wrapper {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

/* Ensure text is readable over background images */
.project__section h2,
.project__section p {
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  position: relative;
  z-index: 3;
}

/* Loading state for project cards */
.project-card.loading {
  opacity: 0.7;
  pointer-events: none;
}

.project-card.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--color-primary);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.modal-background {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-background.open {
  opacity: 1;
}

/* Enhanced Close Button */
.modal-close {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  z-index: 1001;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  backdrop-filter: blur(10px);
  color: var(--color-secondary-text);
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-close:hover {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: #ef4444;
  transform: scale(1.1) rotate(90deg);
}

.modal-close:active {
  transform: scale(0.95) rotate(90deg);
}

/* Loading States */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(84, 98, 255, 0.1);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.modal__image.loading {
  opacity: 0;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  color: var(--color-secondary-text);
  font-size: 0.9rem;
  padding: 2rem;
}

.image-error::before {
  content: "🖼️";
  font-size: 3rem;
  opacity: 0.5;
}

/* Image Zoom Feature */
.modal__image-container.zoomable {
  cursor: zoom-in;
}

.modal__image-container.zoomed {
  cursor: zoom-out;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1002;
  background: rgba(0, 0, 0, 0.95);
  border-radius: 0;
}

.modal__image-container.zoomed .modal__image {
  max-width: 90vw;
  max-height: 90vh;
  width: auto;
  height: auto;
  object-fit: contain;
}

/* Project Stats */
.modal__stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
  padding: 1rem;
  background: rgba(84, 98, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(84, 98, 255, 0.1);
}

.modal__stat {
  text-align: center;
}

.modal__stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-primary);
  display: block;
}

.modal__stat-label {
  font-size: 0.75rem;
  color: var(--color-secondary-text);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: 0.25rem;
}

/* Keyframe Animations */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
  .project__section .container {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.25rem;
  }

  .project__section .container > div:nth-of-type(1) {
    grid-column: 1/3;
  }
}

@media (max-width: 992px) {
  .project__section .container {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    padding: 0 0.5rem;
  }

  .project__section .container > div {
    height: 200px;
  }

  .project__section .container > div:nth-of-type(1) {
    grid-column: 1/3;
  }
}

@media (max-width: 768px) {
  .project__section .container {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-top: 2rem;
  }

  .project__section .container > div {
    height: 180px;
  }

  .overlay {
    padding: 1rem;
  }

  .overlay-description {
    font-size: 0.8rem;
  }

  span.overlay-stack {
    font-size: 0.7rem;
    padding: 0.3rem 0.6rem;
  }
}

@media (max-width: 576px) {
  .project__section .container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .project__section .container > div {
    height: 200px;
  }

  .project__section .container > div:nth-of-type(1) {
    grid-column: 1;
  }

  .project__section h2 {
    text-align: center;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .project__section .container > div,
  .overlay,
  span.overlay-stack,
  .content-wrapper {
    transition: none;
    animation: none;
  }
}

/* Modal Responsive Design */
@media (max-width: 768px) {
  .modal-content {
    grid-template-columns: 1fr;
    max-width: 95vw;
    max-height: 95vh;
    overflow-y: auto;
  }

  .modal__image-container {
    border-radius: 20px 20px 0 0;
    min-height: 250px;
  }

  .modal-info {
    border-radius: 0 0 20px 20px;
    padding: 2rem 1.5rem;
  }

  .modal-info::before {
    border-radius: 20px 20px 0 0;
  }

  .modal__actions {
    flex-direction: column;
  }

  .modal__git-link,
  .modal__demo-link {
    justify-content: center;
    width: 100%;
  }

  .modal__stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .modal-content {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }

  .modal-info {
    padding: 1.5rem 1rem;
    gap: 1rem;
  }

  .modal-close {
    top: 1rem;
    right: 1rem;
    width: 35px;
    height: 35px;
  }

  .modal__stats {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    padding: 0.75rem;
  }

  .modal__stat-value {
    font-size: 1.25rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .project__section .container > div {
    border: 2px solid var(--color-primary);
  }

  .overlay {
    background: rgba(0, 0, 0, 0.95);
  }

  span.overlay-stack {
    border: 2px solid white;
    background: var(--color-primary);
  }

  .modal-content {
    border: 2px solid var(--color-primary);
  }

  .modal__stacks .stack {
    border: 2px solid var(--color-primary);
    background: white;
  }
}

/* Print styles */
@media print {
  .modal-background {
    display: none;
  }
}
