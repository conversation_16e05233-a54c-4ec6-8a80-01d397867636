<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Project Cards Demo</title>
    <link rel="stylesheet" href="src/style/style.css">
    <link rel="stylesheet" href="src/style/project.css">
    <link href="https://fonts.googleapis.com/css2?family=Raleway:wght@300;400;500;600;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div id="content">
        <section class="hero project__section" id="projects">
            <div class="container">
                <!-- Recipe App Card -->
                <div class="project-card bg1" tabindex="0" role="listitem" aria-label="Recipe App project">
                    <div class="content-wrapper">
                        <h2 class="project-title">16 <span>| 24</span></h2>
                        <p class="project-label">Recipe App</p>
                    </div>
                    <div class="overlay" role="region" aria-labelledby="project-title-0" aria-describedby="project-desc-0">
                        <p class="overlay-description">Vanilla JS project with a focus on creating a visually appealing and interactive user interface.</p>
                        <div class="overlay-stacks" role="list" aria-label="Technologies used">
                            <span class="overlay-stack" role="listitem" style="--i: 0">HTML5</span>
                            <span class="overlay-stack" role="listitem" style="--i: 1">CSS3</span>
                            <span class="overlay-stack" role="listitem" style="--i: 2">JavaScript</span>
                        </div>
                    </div>
                </div>

                <!-- Health Tracker Card -->
                <div class="project-card bg2" tabindex="0" role="listitem" aria-label="Health Tracker project">
                    <div class="content-wrapper">
                        <h2 class="project-title"><i class="fas fa-battery-three-quarters"></i></h2>
                        <p class="project-label">Health Tracker</p>
                    </div>
                    <div class="overlay" role="region">
                        <p class="overlay-description">Simple web application that allows users to track their breathing patterns and monitor their respiratory health.</p>
                        <div class="overlay-stacks" role="list" aria-label="Technologies used">
                            <span class="overlay-stack" role="listitem" style="--i: 0">HTML5</span>
                            <span class="overlay-stack" role="listitem" style="--i: 1">CSS3</span>
                            <span class="overlay-stack" role="listitem" style="--i: 2">JavaScript</span>
                            <span class="overlay-stack" role="listitem" style="--i: 3">Webpack</span>
                        </div>
                    </div>
                </div>

                <!-- React App Card -->
                <div class="project-card bg1" tabindex="0" role="listitem" aria-label="React App project">
                    <div class="content-wrapper">
                        <h2 class="project-title"><i class="fab fa-react"></i></h2>
                        <p class="project-label">React App</p>
                    </div>
                    <div class="overlay" role="region">
                        <p class="overlay-description">Modern React application built with hooks and context API for state management.</p>
                        <div class="overlay-stacks" role="list" aria-label="Technologies used">
                            <span class="overlay-stack" role="listitem" style="--i: 0">React</span>
                            <span class="overlay-stack" role="listitem" style="--i: 1">JavaScript</span>
                            <span class="overlay-stack" role="listitem" style="--i: 2">CSS3</span>
                        </div>
                    </div>
                </div>

                <!-- Temperature Monitor Card -->
                <div class="project-card bg2" tabindex="0" role="listitem" aria-label="Temperature Monitor project">
                    <div class="content-wrapper">
                        <h2 class="project-title">36 <span>°</span></h2>
                        <p class="project-label">Temperature Monitor</p>
                    </div>
                    <div class="overlay" role="region">
                        <p class="overlay-description">IoT temperature monitoring system with real-time data visualization and alerts.</p>
                        <div class="overlay-stacks" role="list" aria-label="Technologies used">
                            <span class="overlay-stack" role="listitem" style="--i: 0">Node.js</span>
                            <span class="overlay-stack" role="listitem" style="--i: 1">Socket.io</span>
                            <span class="overlay-stack" role="listitem" style="--i: 2">Chart.js</span>
                        </div>
                    </div>
                </div>

                <!-- Sleep Tracker Card -->
                <div class="project-card bg1" tabindex="0" role="listitem" aria-label="Sleep Tracker project">
                    <div class="content-wrapper">
                        <h2 class="project-title"><i class="fas fa-bed"></i></h2>
                        <p class="project-label">Sleep Tracker</p>
                    </div>
                    <div class="overlay" role="region">
                        <p class="overlay-description">Sleep pattern analysis tool with detailed insights and recommendations for better sleep.</p>
                        <div class="overlay-stacks" role="list" aria-label="Technologies used">
                            <span class="overlay-stack" role="listitem" style="--i: 0">Vue.js</span>
                            <span class="overlay-stack" role="listitem" style="--i: 1">D3.js</span>
                            <span class="overlay-stack" role="listitem" style="--i: 2">Firebase</span>
                        </div>
                    </div>
                </div>

                <!-- Heart Rate Monitor Card -->
                <div class="project-card bg2" tabindex="0" role="listitem" aria-label="Heart Rate Monitor project">
                    <div class="content-wrapper">
                        <h2 class="project-title">98 <span>bpm</span></h2>
                        <p class="project-label">Heart Rate Monitor</p>
                    </div>
                    <div class="overlay" role="region">
                        <p class="overlay-description">Real-time heart rate monitoring with data analytics and health insights dashboard.</p>
                        <div class="overlay-stacks" role="list" aria-label="Technologies used">
                            <span class="overlay-stack" role="listitem" style="--i: 0">React</span>
                            <span class="overlay-stack" role="listitem" style="--i: 1">TypeScript</span>
                            <span class="overlay-stack" role="listitem" style="--i: 2">WebRTC</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <script>
        // Add click handlers for demo
        document.querySelectorAll('.project-card').forEach(card => {
            card.addEventListener('click', () => {
                console.log('Project card clicked:', card.querySelector('.project-label').textContent);
            });
            
            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    console.log('Project card activated:', card.querySelector('.project-label').textContent);
                }
            });
        });
    </script>
</body>
</html>
