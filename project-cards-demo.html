<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Enhanced Project Cards Demo</title>
    <link rel="stylesheet" href="src/style/style.css" />
    <link rel="stylesheet" href="src/style/project.css" />
    <link
      href="https://fonts.googleapis.com/css2?family=Raleway:wght@300;400;500;600;700;900&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
  </head>
  <body>
    <div id="content">
      <section class="hero project__section" id="projects">
        <div class="container">
          <!-- Recipe App Card -->
          <div
            class="project-card bg1"
            tabindex="0"
            role="listitem"
            aria-label="Recipe App project"
          >
            <div class="content-wrapper">
              <h2 class="project-title">16 <span>| 24</span></h2>
              <p class="project-label">Recipe App</p>
            </div>
            <div
              class="overlay"
              role="region"
              aria-labelledby="project-title-0"
              aria-describedby="project-desc-0"
            >
              <p class="overlay-description">
                Vanilla JS project with a focus on creating a visually appealing
                and interactive user interface.
              </p>
              <div
                class="overlay-stacks"
                role="list"
                aria-label="Technologies used"
              >
                <span class="overlay-stack" role="listitem" style="--i: 0"
                  >HTML5</span
                >
                <span class="overlay-stack" role="listitem" style="--i: 1"
                  >CSS3</span
                >
                <span class="overlay-stack" role="listitem" style="--i: 2"
                  >JavaScript</span
                >
              </div>
            </div>
          </div>

          <!-- Health Tracker Card -->
          <div
            class="project-card bg2"
            tabindex="0"
            role="listitem"
            aria-label="Health Tracker project"
          >
            <div class="content-wrapper">
              <h2 class="project-title">
                <i class="fas fa-battery-three-quarters"></i>
              </h2>
              <p class="project-label">Health Tracker</p>
            </div>
            <div class="overlay" role="region">
              <p class="overlay-description">
                Simple web application that allows users to track their
                breathing patterns and monitor their respiratory health.
              </p>
              <div
                class="overlay-stacks"
                role="list"
                aria-label="Technologies used"
              >
                <span class="overlay-stack" role="listitem" style="--i: 0"
                  >HTML5</span
                >
                <span class="overlay-stack" role="listitem" style="--i: 1"
                  >CSS3</span
                >
                <span class="overlay-stack" role="listitem" style="--i: 2"
                  >JavaScript</span
                >
                <span class="overlay-stack" role="listitem" style="--i: 3"
                  >Webpack</span
                >
              </div>
            </div>
          </div>

          <!-- React App Card -->
          <div
            class="project-card bg1"
            tabindex="0"
            role="listitem"
            aria-label="React App project"
          >
            <div class="content-wrapper">
              <h2 class="project-title"><i class="fab fa-react"></i></h2>
              <p class="project-label">React App</p>
            </div>
            <div class="overlay" role="region">
              <p class="overlay-description">
                Modern React application built with hooks and context API for
                state management.
              </p>
              <div
                class="overlay-stacks"
                role="list"
                aria-label="Technologies used"
              >
                <span class="overlay-stack" role="listitem" style="--i: 0"
                  >React</span
                >
                <span class="overlay-stack" role="listitem" style="--i: 1"
                  >JavaScript</span
                >
                <span class="overlay-stack" role="listitem" style="--i: 2"
                  >CSS3</span
                >
              </div>
            </div>
          </div>

          <!-- Temperature Monitor Card -->
          <div
            class="project-card bg2"
            tabindex="0"
            role="listitem"
            aria-label="Temperature Monitor project"
          >
            <div class="content-wrapper">
              <h2 class="project-title">36 <span>°</span></h2>
              <p class="project-label">Temperature Monitor</p>
            </div>
            <div class="overlay" role="region">
              <p class="overlay-description">
                IoT temperature monitoring system with real-time data
                visualization and alerts.
              </p>
              <div
                class="overlay-stacks"
                role="list"
                aria-label="Technologies used"
              >
                <span class="overlay-stack" role="listitem" style="--i: 0"
                  >Node.js</span
                >
                <span class="overlay-stack" role="listitem" style="--i: 1"
                  >Socket.io</span
                >
                <span class="overlay-stack" role="listitem" style="--i: 2"
                  >Chart.js</span
                >
              </div>
            </div>
          </div>

          <!-- Sleep Tracker Card -->
          <div
            class="project-card bg1"
            tabindex="0"
            role="listitem"
            aria-label="Sleep Tracker project"
          >
            <div class="content-wrapper">
              <h2 class="project-title"><i class="fas fa-bed"></i></h2>
              <p class="project-label">Sleep Tracker</p>
            </div>
            <div class="overlay" role="region">
              <p class="overlay-description">
                Sleep pattern analysis tool with detailed insights and
                recommendations for better sleep.
              </p>
              <div
                class="overlay-stacks"
                role="list"
                aria-label="Technologies used"
              >
                <span class="overlay-stack" role="listitem" style="--i: 0"
                  >Vue.js</span
                >
                <span class="overlay-stack" role="listitem" style="--i: 1"
                  >D3.js</span
                >
                <span class="overlay-stack" role="listitem" style="--i: 2"
                  >Firebase</span
                >
              </div>
            </div>
          </div>

          <!-- Heart Rate Monitor Card -->
          <div
            class="project-card bg2"
            tabindex="0"
            role="listitem"
            aria-label="Heart Rate Monitor project"
          >
            <div class="content-wrapper">
              <h2 class="project-title">98 <span>bpm</span></h2>
              <p class="project-label">Heart Rate Monitor</p>
            </div>
            <div class="overlay" role="region">
              <p class="overlay-description">
                Real-time heart rate monitoring with data analytics and health
                insights dashboard.
              </p>
              <div
                class="overlay-stacks"
                role="list"
                aria-label="Technologies used"
              >
                <span class="overlay-stack" role="listitem" style="--i: 0"
                  >React</span
                >
                <span class="overlay-stack" role="listitem" style="--i: 1"
                  >TypeScript</span
                >
                <span class="overlay-stack" role="listitem" style="--i: 2"
                  >WebRTC</span
                >
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <script>
      // Enhanced Modal Demo
      class ProjectModal {
        constructor(project) {
          this.project = project;
          this.modalBackground = null;
          this.focusedElementBeforeModal = null;
          this.imageLoaded = false;
        }

        createElement(type, attributes = {}, content = null) {
          const element = document.createElement(type);
          Object.entries(attributes).forEach(([key, value]) =>
            element.setAttribute(key, value)
          );
          if (content) element.textContent = content;
          return element;
        }

        createModal() {
          this.focusedElementBeforeModal = document.activeElement;

          this.modalBackground = this.createElement("div", {
            class: "modal-background",
            role: "dialog",
            "aria-modal": "true",
            "aria-labelledby": "modal-title",
            "aria-describedby": "modal-description",
          });

          const modalContent = this.createElement("div", {
            class: "modal-content",
            role: "document",
          });

          const closeButton = this.createCloseButton();
          const imageContainer = this.createImageContainer();
          const modalInfo = this.createModalInfo();

          modalContent.append(closeButton, imageContainer, modalInfo);
          this.modalBackground.appendChild(modalContent);
          document.body.appendChild(this.modalBackground);

          this.addEventListeners();

          requestAnimationFrame(() => {
            closeButton.focus();
            this.modalBackground.classList.add("open");
          });
        }

        createCloseButton() {
          const closeButton = this.createElement(
            "button",
            {
              class: "modal-close",
              "aria-label": "Close project details",
              type: "button",
            },
            "×"
          );
          closeButton.addEventListener("click", () => this.close());
          return closeButton;
        }

        createImageContainer() {
          const imageContainer = this.createElement("figure", {
            class: "modal__image-container zoomable",
            role: "img",
            "aria-label": `Screenshot of ${this.project.label} project`,
          });

          const modalImage = this.createElement("img", {
            class: "modal__image",
            src: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop",
            alt: `Screenshot of ${this.project.label} project`,
          });

          imageContainer.appendChild(modalImage);
          this.imageLoaded = true;

          // Add zoom functionality
          imageContainer.addEventListener("click", () => {
            if (this.imageLoaded) {
              imageContainer.classList.toggle("zoomed");
            }
          });

          return imageContainer;
        }

        createModalInfo() {
          const modalInfo = this.createElement("div", {
            class: "modal-info",
            role: "region",
            "aria-label": "Project information",
          });

          const modalTitle = this.createElement(
            "h2",
            {
              class: "modal-title",
              id: "modal-title",
            },
            this.project.label
          );

          const modalDescription = this.createElement(
            "p",
            {
              class: "modal__description",
              id: "modal-description",
            },
            this.project.details
          );

          const modalStacks = this.createStacksList();
          const modalStats = this.createProjectStats();
          const actionButtons = this.createActionButtons();

          modalInfo.append(
            modalTitle,
            modalDescription,
            modalStacks,
            modalStats,
            actionButtons
          );
          return modalInfo;
        }

        createStacksList() {
          const modalStacks = this.createElement("div", {
            class: "modal__stacks",
            role: "list",
            "aria-label": "Technologies used in this project",
          });

          this.project.stacks.forEach((stack) => {
            const stackSpan = this.createElement(
              "span",
              {
                class: "stack",
                role: "listitem",
              },
              stack
            );
            modalStacks.appendChild(stackSpan);
          });

          return modalStacks;
        }

        createProjectStats() {
          const stats = {
            year: "2024",
            duration: "3 weeks",
            status: "Completed",
          };

          const statsContainer = this.createElement("div", {
            class: "modal__stats",
            role: "region",
            "aria-label": "Project statistics",
          });

          Object.entries(stats).forEach(([key, value]) => {
            const stat = this.createElement("div", { class: "modal__stat" });
            const statValue = this.createElement(
              "span",
              { class: "modal__stat-value" },
              value
            );
            const statLabel = this.createElement(
              "span",
              { class: "modal__stat-label" },
              key
            );

            stat.append(statValue, statLabel);
            statsContainer.appendChild(stat);
          });

          return statsContainer;
        }

        createActionButtons() {
          const actionsContainer = this.createElement("div", {
            class: "modal__actions",
            role: "region",
            "aria-label": "Project actions",
          });

          // GitHub Link
          const gitLink = this.createElement("a", {
            class: "modal__git-link",
            href: "#",
            target: "_blank",
            rel: "noopener noreferrer",
            "aria-label": `View ${this.project.label} source code on GitHub`,
          });

          const gitIcon = this.createElement("i", {
            class: "fab fa-github",
            "aria-hidden": "true",
          });

          const gitText = this.createElement("span", {}, "View Code");
          gitLink.append(gitIcon, gitText);
          actionsContainer.appendChild(gitLink);

          // Demo Link
          const demoLink = this.createElement("a", {
            class: "modal__demo-link",
            href: "#",
            target: "_blank",
            rel: "noopener noreferrer",
            "aria-label": `View ${this.project.label} live demo`,
          });

          const demoIcon = this.createElement("i", {
            class: "fas fa-external-link-alt",
            "aria-hidden": "true",
          });

          const demoText = this.createElement("span", {}, "Live Demo");
          demoLink.append(demoIcon, demoText);
          actionsContainer.appendChild(demoLink);

          return actionsContainer;
        }

        addEventListeners() {
          this.modalBackground.addEventListener("click", (e) => {
            if (e.target === this.modalBackground) {
              this.close();
            }
          });

          this.modalBackground.addEventListener("keydown", (e) => {
            if (e.key === "Escape") {
              this.close();
            }
          });
        }

        open() {
          this.createModal();
        }

        close() {
          this.modalBackground.classList.remove("open");
          setTimeout(() => {
            this.modalBackground.remove();
            if (this.focusedElementBeforeModal) {
              this.focusedElementBeforeModal.focus();
            }
          }, 300);
        }
      }

      // Sample project data
      const projects = [
        {
          label: "Recipe App",
          details:
            "Vanilla JS project with a focus on creating a visually appealing and interactive user interface. Features include recipe search, ingredient filtering, and meal planning capabilities.",
          stacks: ["HTML5", "CSS3", "JavaScript", "Local Storage"],
        },
        {
          label: "Health Tracker",
          details:
            "A comprehensive web application that allows users to track their breathing patterns and monitor their respiratory health with real-time data visualization.",
          stacks: ["HTML5", "CSS3", "JavaScript", "Webpack", "Chart.js"],
        },
        {
          label: "React App",
          details:
            "A modern React application built with hooks and context API, featuring responsive design and optimized performance for seamless user experience.",
          stacks: ["React", "JavaScript", "CSS3", "Webpack", "Context API"],
        },
      ];

      // Add click handlers for demo
      document.querySelectorAll(".project-card").forEach((card, index) => {
        const project = projects[index] || projects[0];

        card.addEventListener("click", () => {
          const modal = new ProjectModal(project);
          modal.open();
        });

        card.addEventListener("keydown", (e) => {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            const modal = new ProjectModal(project);
            modal.open();
          }
        });
      });
    </script>
  </body>
</html>
